extends Node2D

class_name Bucket

var bucket_type: String = ""
var grid_position: Vector3i = Vector3i.ZERO
var game_manager: <PERSON>Manager

@onready var sprite: Sprite2D = $Sprite2D

# Bucket type to texture mapping
var bucket_textures = {
	"wood": "res://assets/bucket/wood.png",
	"stone": "res://assets/bucket/stone.png", 
	"iron": "res://assets/bucket/iron.png",
	"gold": "res://assets/bucket/gold.png",
	"hardstone": "res://assets/bucket/hardstone.png",
	"mistery": "res://assets/bucket/mistery.png",
	"broken": "res://assets/bucket/broken.png"
}

func _ready():
	# Create sprite if it doesn't exist
	if not sprite:
		sprite = Sprite2D.new()
		add_child(sprite)

	# Keep sprite at reasonable size but let transparent areas overlap
	sprite.scale = Vector2(0.06, 0.06)  # Slightly larger than grid to ensure good visibility

	# No offset needed - let the transparent areas naturally overlap
	sprite.offset = Vector2(0, 0)

func setup(type: String, pos: Vector3i, manager: GameManager):
	"""Initialize the bucket with type, position, and game manager reference"""
	bucket_type = type
	grid_position = pos
	game_manager = manager
	
	# Load and set the appropriate texture
	if bucket_type in bucket_textures:
		var texture = load(bucket_textures[bucket_type])
		if sprite:
			sprite.texture = texture
	
	# Adjust visual layering based on stack height
	z_index = grid_position.z

func update_grid_position(new_pos: Vector3i):
	"""Update the bucket's grid position"""
	grid_position = new_pos
	z_index = grid_position.z

func get_bucket_type() -> String:
	"""Get the bucket type"""
	return bucket_type

func get_grid_position() -> Vector3i:
	"""Get the current grid position"""
	return grid_position

func can_be_pushed() -> bool:
	"""Check if this bucket can be pushed (all buckets can be pushed for now)"""
	return true

func on_pushed():
	"""Called when the bucket is pushed"""
	# Add any special effects or sounds here
	pass

func _to_string() -> String:
	"""String representation for debugging"""
	return "Bucket(%s) at %s" % [bucket_type, grid_position]
