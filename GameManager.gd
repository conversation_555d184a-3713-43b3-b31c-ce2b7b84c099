extends Node2D

class_name GameManager

# Grid constants
const GRID_SIZE = 9
const CELL_SIZE = 40  # Smaller grid to make sprites overlap their transparent areas
const MAX_STACK_HEIGHT = 10  # Maximum buckets that can be stacked

# Grid data structure: [x][y][z] where z is the stack height
var grid: Array = []
var worm_position: Vector3i = Vector3i.ZERO
var buckets: Array[Node2D] = []
var worm: Node2D

# Bucket types available
var bucket_types = [
	"wood", "stone", "iron", "gold", "hardstone", "mistery", "broken"
]

# Preloaded scenes
var bucket_scene = preload("res://Bucket.tscn")
var worm_scene = preload("res://Worm.tscn")

signal game_started
signal bucket_moved(from_pos: Vector3i, to_pos: Vector3i)
signal worm_moved(from_pos: Vector3i, to_pos: Vector3i)

func _ready():
	initialize_grid()
	setup_game()

func initialize_grid():
	"""Initialize the 3D grid structure"""
	grid = []
	for x in range(GRID_SIZE):
		grid.append([])
		for y in range(GRID_SIZE):
			grid[x].append([])
			for z in range(MAX_STACK_HEIGHT):
				grid[x][y].append(null)

func setup_game():
	"""Setup the initial game state"""
	spawn_worm()
	spawn_buckets()
	game_started.emit()

func spawn_worm():
	"""Spawn the worm at a random empty position"""
	var empty_positions = get_empty_positions()
	if empty_positions.size() > 0:
		var spawn_pos = empty_positions[randi() % empty_positions.size()]
		worm_position = Vector3i(spawn_pos.x, spawn_pos.y, 0)
		
		worm = worm_scene.instantiate()
		add_child(worm)
		worm.position = grid_to_world_position(worm_position)
		worm.setup(self)
		
		# Mark grid position as occupied
		grid[worm_position.x][worm_position.y][0] = worm

func spawn_buckets():
	"""Spawn random buckets on the grid"""
	var num_buckets = randi_range(10, 20)  # Random number of buckets
	
	for i in range(num_buckets):
		spawn_random_bucket()

func spawn_random_bucket():
	"""Spawn a single random bucket"""
	var empty_positions = get_empty_positions()
	if empty_positions.size() == 0:
		return  # No more space
	
	var spawn_pos = empty_positions[randi() % empty_positions.size()]
	var bucket_type = bucket_types[randi() % bucket_types.size()]
	
	# Find the stack height at this position
	var stack_height = get_stack_height(spawn_pos.x, spawn_pos.y)
	if stack_height >= MAX_STACK_HEIGHT:
		return  # Stack too high
	
	var bucket = bucket_scene.instantiate()
	add_child(bucket)
	buckets.append(bucket)
	
	var bucket_pos = Vector3i(spawn_pos.x, spawn_pos.y, stack_height)
	bucket.setup(bucket_type, bucket_pos, self)
	bucket.position = grid_to_world_position(bucket_pos)
	
	# Mark grid position as occupied
	grid[bucket_pos.x][bucket_pos.y][bucket_pos.z] = bucket

func get_empty_positions() -> Array[Vector2i]:
	"""Get all empty ground positions (z=0)"""
	var empty_positions: Array[Vector2i] = []
	
	for x in range(GRID_SIZE):
		for y in range(GRID_SIZE):
			if grid[x][y][0] == null:
				empty_positions.append(Vector2i(x, y))
	
	return empty_positions

func get_stack_height(x: int, y: int) -> int:
	"""Get the current stack height at position (x, y)"""
	for z in range(MAX_STACK_HEIGHT):
		if grid[x][y][z] == null:
			return z
	return MAX_STACK_HEIGHT

func grid_to_world_position(grid_pos: Vector3i) -> Vector2:
	"""Convert grid position to world position"""
	var world_x = grid_pos.x * CELL_SIZE
	var world_y = grid_pos.y * CELL_SIZE - (grid_pos.z * CELL_SIZE * 0.2)  # Slightly more stacking offset for visibility
	return Vector2(world_x, world_y)

func world_to_grid_position(world_pos: Vector2) -> Vector2i:
	"""Convert world position to grid position (x, y only)"""
	var grid_x = int(world_pos.x / CELL_SIZE)
	var grid_y = int(world_pos.y / CELL_SIZE)
	return Vector2i(grid_x, grid_y)

func is_valid_position(pos: Vector3i) -> bool:
	"""Check if a grid position is valid"""
	return pos.x >= 0 and pos.x < GRID_SIZE and pos.y >= 0 and pos.y < GRID_SIZE and pos.z >= 0 and pos.z < MAX_STACK_HEIGHT

func is_position_empty(pos: Vector3i) -> bool:
	"""Check if a grid position is empty"""
	if not is_valid_position(pos):
		return false
	return grid[pos.x][pos.y][pos.z] == null

func get_entity_at_position(pos: Vector3i) -> Node2D:
	"""Get the entity at a specific grid position"""
	if not is_valid_position(pos):
		return null
	return grid[pos.x][pos.y][pos.z]

func move_entity(entity: Node2D, from_pos: Vector3i, to_pos: Vector3i) -> bool:
	"""Move an entity from one position to another"""
	if not is_valid_position(to_pos):
		return false
	
	if not is_position_empty(to_pos):
		return false
	
	# Clear old position
	if is_valid_position(from_pos):
		grid[from_pos.x][from_pos.y][from_pos.z] = null
	
	# Set new position
	grid[to_pos.x][to_pos.y][to_pos.z] = entity
	entity.position = grid_to_world_position(to_pos)
	
	return true

func can_push_bucket(bucket_pos: Vector3i, direction: Vector2i) -> bool:
	"""Check if a bucket can be pushed in a direction"""
	var target_pos = Vector3i(bucket_pos.x + direction.x, bucket_pos.y + direction.y, bucket_pos.z)
	
	if not is_valid_position(target_pos):
		return false
	
	# Check if target position is empty or if we can stack
	if bucket_pos.z == 0:  # Ground level bucket
		# Can push to empty ground or stack on existing bucket
		var ground_target = Vector3i(target_pos.x, target_pos.y, 0)
		if is_position_empty(ground_target):
			return true
		else:
			# Try to stack on existing bucket
			var stack_height = get_stack_height(target_pos.x, target_pos.y)
			var stack_target = Vector3i(target_pos.x, target_pos.y, stack_height)
			return is_valid_position(stack_target) and is_position_empty(stack_target)
	
	return is_position_empty(target_pos)

func push_bucket(bucket_pos: Vector3i, direction: Vector2i) -> bool:
	"""Push a bucket in a direction"""
	if not can_push_bucket(bucket_pos, direction):
		return false
	
	var bucket = get_entity_at_position(bucket_pos)
	if bucket == null:
		return false
	
	var target_pos: Vector3i
	
	if bucket_pos.z == 0:  # Ground level bucket
		var ground_target = Vector3i(bucket_pos.x + direction.x, bucket_pos.y + direction.y, 0)
		if is_position_empty(ground_target):
			target_pos = ground_target
		else:
			# Stack on existing bucket
			var stack_height = get_stack_height(ground_target.x, ground_target.y)
			target_pos = Vector3i(ground_target.x, ground_target.y, stack_height)
	else:
		target_pos = Vector3i(bucket_pos.x + direction.x, bucket_pos.y + direction.y, bucket_pos.z)
	
	if move_entity(bucket, bucket_pos, target_pos):
		bucket.update_grid_position(target_pos)
		bucket_moved.emit(bucket_pos, target_pos)
		return true
	
	return false
