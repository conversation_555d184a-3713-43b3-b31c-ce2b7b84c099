extends Node2D

class_name GameManager

# Grid constants
const GRID_SIZE = 9
const CELL_SIZE = 40  # Smaller grid to make sprites overlap their transparent areas
const MAX_STACK_HEIGHT = 10  # Maximum buckets that can be stacked

# Grid data structure: [x][y][z] where z is the stack height
var grid: Array = []
var worm_position: Vector3i = Vector3i.ZERO
var buckets: Array[Node2D] = []
var worm: Node2D
var is_processing_turn: bool = false

# Bucket types available
var bucket_types = [
	"wood", "stone", "iron", "gold", "hardstone", "mistery", "broken"
]

# Preloaded scenes
var bucket_scene = preload("res://Bucket.tscn")
var worm_scene = preload("res://Worm.tscn")

signal game_started
signal bucket_moved(from_pos: Vector3i, to_pos: Vector3i)
signal worm_moved(from_pos: Vector3i, to_pos: Vector3i)

func _ready():
	initialize_grid()
	setup_game()

func initialize_grid():
	"""Initialize the 3D grid structure"""
	grid = []
	for x in range(GRID_SIZE):
		grid.append([])
		for y in range(GRID_SIZE):
			grid[x].append([])
			for z in range(MAX_STACK_HEIGHT):
				grid[x][y].append(null)



func setup_game():
	"""Setup the initial game state"""
	spawn_worm()
	spawn_buckets()
	game_started.emit()

func spawn_worm():
	"""Spawn the worm at a random empty position"""
	var empty_positions = get_empty_positions()
	if empty_positions.size() > 0:
		var spawn_pos = empty_positions[randi() % empty_positions.size()]
		worm_position = Vector3i(spawn_pos.x, spawn_pos.y, 0)
		
		worm = worm_scene.instantiate()
		add_child(worm)
		worm.position = grid_to_world_position(worm_position)
		worm.setup(self)
		
		# Mark grid position as occupied
		grid[worm_position.x][worm_position.y][0] = worm

func spawn_buckets():
	"""Spawn random buckets on the grid"""
	var num_buckets = randi_range(10, 20)  # Random number of buckets

	for i in range(num_buckets):
		spawn_random_bucket()

	# Create some test stacks for jumping
	create_test_stacks()

func spawn_random_bucket():
	"""Spawn a single random bucket"""
	var empty_positions = get_empty_positions()
	if empty_positions.size() == 0:
		return  # No more space
	
	var spawn_pos = empty_positions[randi() % empty_positions.size()]
	var bucket_type = bucket_types[randi() % bucket_types.size()]
	
	# Find the stack height at this position
	var stack_height = get_stack_height(spawn_pos.x, spawn_pos.y)
	if stack_height >= MAX_STACK_HEIGHT:
		return  # Stack too high
	
	var bucket = bucket_scene.instantiate()
	add_child(bucket)
	buckets.append(bucket)
	
	var bucket_pos = Vector3i(spawn_pos.x, spawn_pos.y, stack_height)
	bucket.setup(bucket_type, bucket_pos, self)
	bucket.position = grid_to_world_position(bucket_pos)
	
	# Mark grid position as occupied
	grid[bucket_pos.x][bucket_pos.y][bucket_pos.z] = bucket

func get_empty_positions() -> Array[Vector2i]:
	"""Get all empty ground positions (z=0)"""
	var empty_positions: Array[Vector2i] = []
	
	for x in range(GRID_SIZE):
		for y in range(GRID_SIZE):
			if grid[x][y][0] == null:
				empty_positions.append(Vector2i(x, y))
	
	return empty_positions

func get_stack_height(x: int, y: int) -> int:
	"""Get the current stack height at position (x, y)"""
	for z in range(MAX_STACK_HEIGHT):
		if grid[x][y][z] == null:
			return z
	return MAX_STACK_HEIGHT

func grid_to_world_position(grid_pos: Vector3i) -> Vector2:
	"""Convert grid position to world position"""
	var world_x = grid_pos.x * CELL_SIZE
	var world_y = grid_pos.y * CELL_SIZE - (grid_pos.z * CELL_SIZE * 0.3)  # More stacking offset for better visibility
	return Vector2(world_x, world_y)

func world_to_grid_position(world_pos: Vector2) -> Vector2i:
	"""Convert world position to grid position (x, y only)"""
	var grid_x = int(world_pos.x / CELL_SIZE)
	var grid_y = int(world_pos.y / CELL_SIZE)
	return Vector2i(grid_x, grid_y)

func is_valid_position(pos: Vector3i) -> bool:
	"""Check if a grid position is valid"""
	return pos.x >= 0 and pos.x < GRID_SIZE and pos.y >= 0 and pos.y < GRID_SIZE and pos.z >= 0 and pos.z < MAX_STACK_HEIGHT

func is_position_empty(pos: Vector3i) -> bool:
	"""Check if a grid position is empty"""
	if not is_valid_position(pos):
		return false
	return grid[pos.x][pos.y][pos.z] == null

func get_entity_at_position(pos: Vector3i) -> Node2D:
	"""Get the entity at a specific grid position"""
	if not is_valid_position(pos):
		return null
	return grid[pos.x][pos.y][pos.z]

func move_entity(entity: Node2D, from_pos: Vector3i, to_pos: Vector3i) -> bool:
	"""Move an entity from one position to another"""
	if not is_valid_position(to_pos):
		return false

	if not is_position_empty(to_pos):
		return false

	# Clear old position
	if is_valid_position(from_pos):
		grid[from_pos.x][from_pos.y][from_pos.z] = null

	# Set new position
	grid[to_pos.x][to_pos.y][to_pos.z] = entity
	entity.position = grid_to_world_position(to_pos)

	# Set z_index for proper layering (higher z = higher layer)
	entity.z_index = to_pos.z

	return true

func can_push_bucket(bucket_pos: Vector3i, direction: Vector2i) -> bool:
	"""Check if a bucket can be pushed in a direction"""
	var target_pos = Vector3i(bucket_pos.x + direction.x, bucket_pos.y + direction.y, bucket_pos.z)

	if not is_valid_position(target_pos):
		return false

	# Check if target position is empty
	if not is_position_empty(target_pos):
		return false

	# For buckets not on ground level, they can only be pushed if there's support below the target
	if bucket_pos.z > 0:
		var support_pos = Vector3i(target_pos.x, target_pos.y, target_pos.z - 1)
		if is_position_empty(support_pos):
			return false  # No support below target position

	return true

func push_bucket(bucket_pos: Vector3i, direction: Vector2i) -> bool:
	"""Push a bucket in a direction"""
	if not can_push_bucket(bucket_pos, direction):
		return false

	var bucket = get_entity_at_position(bucket_pos)
	if bucket == null:
		return false

	var target_pos = Vector3i(bucket_pos.x + direction.x, bucket_pos.y + direction.y, bucket_pos.z)

	if move_entity(bucket, bucket_pos, target_pos):
		bucket.update_grid_position(target_pos)
		bucket_moved.emit(bucket_pos, target_pos)

		# After moving, check if bucket needs to fall
		apply_gravity_to_bucket(target_pos)

		return true

	return false

func apply_gravity_to_bucket(bucket_pos: Vector3i):
	"""Apply gravity to a bucket, making it fall if there's no support below"""
	var bucket = get_entity_at_position(bucket_pos)
	if bucket == null or bucket_pos.z == 0:
		return  # No bucket or already on ground

	# Check if there's support below
	var below_pos = Vector3i(bucket_pos.x, bucket_pos.y, bucket_pos.z - 1)
	if not is_position_empty(below_pos):
		return  # There's support below

	# Find the lowest empty position
	var fall_to_z = 0
	for z in range(bucket_pos.z - 1, -1, -1):
		var check_pos = Vector3i(bucket_pos.x, bucket_pos.y, z)
		if is_position_empty(check_pos):
			fall_to_z = z
		else:
			fall_to_z = z + 1
			break

	# Move bucket to the fall position
	var fall_pos = Vector3i(bucket_pos.x, bucket_pos.y, fall_to_z)
	if fall_pos != bucket_pos:
		move_entity(bucket, bucket_pos, fall_pos)
		bucket.update_grid_position(fall_pos)
		bucket_moved.emit(bucket_pos, fall_pos)

func apply_gravity_to_all_buckets():
	"""Apply gravity to all buckets in the game"""
	# Process from bottom to top to avoid conflicts
	for z in range(1, MAX_STACK_HEIGHT):
		for x in range(GRID_SIZE):
			for y in range(GRID_SIZE):
				var pos = Vector3i(x, y, z)
				if get_entity_at_position(pos) is Bucket:
					apply_gravity_to_bucket(pos)

func can_jump_to_position(from_pos: Vector3i, direction: Vector2i) -> Vector3i:
	"""Check if worm can jump in a direction and return the landing position"""
	var target_x = from_pos.x + direction.x
	var target_y = from_pos.y + direction.y

	if target_x < 0 or target_x >= GRID_SIZE or target_y < 0 or target_y >= GRID_SIZE:
		return Vector3i(-1, -1, -1)  # Invalid position

	# Find the highest occupied position at target location
	var landing_z = 0
	for z in range(MAX_STACK_HEIGHT - 1, -1, -1):
		var check_pos = Vector3i(target_x, target_y, z)
		if not is_position_empty(check_pos):
			landing_z = z + 1
			break

	var landing_pos = Vector3i(target_x, target_y, landing_z)

	# Check if landing position is valid and empty
	if is_valid_position(landing_pos) and is_position_empty(landing_pos):
		return landing_pos

	return Vector3i(-1, -1, -1)  # Can't jump there

func process_turn():
	"""Process a complete turn: apply gravity to all buckets after any movement"""
	if is_processing_turn:
		return

	is_processing_turn = true

	# Apply gravity to all buckets
	apply_gravity_to_all_buckets()

	is_processing_turn = false

func can_process_input() -> bool:
	"""Check if input can be processed (not during turn processing)"""
	return not is_processing_turn

func create_test_stacks():
	"""Create some test stacks for testing jumping functionality"""
	# Create a 2-high stack
	if GRID_SIZE >= 3:
		create_stack_at_position(Vector2i(2, 2), 2)
		create_stack_at_position(Vector2i(4, 4), 3)
		create_stack_at_position(Vector2i(6, 3), 1)

func create_stack_at_position(pos: Vector2i, height: int):
	"""Create a stack of buckets at the specified position"""
	if not is_valid_position(Vector3i(pos.x, pos.y, 0)):
		return

	for z in range(height):
		var bucket_pos = Vector3i(pos.x, pos.y, z)
		if not is_position_empty(bucket_pos):
			continue  # Position already occupied

		var bucket = bucket_scene.instantiate()
		add_child(bucket)
		buckets.append(bucket)

		var bucket_type = bucket_types[randi() % bucket_types.size()]
		bucket.setup(bucket_type, bucket_pos, self)
		bucket.position = grid_to_world_position(bucket_pos)

		# Mark grid position as occupied
		grid[bucket_pos.x][bucket_pos.y][bucket_pos.z] = bucket
