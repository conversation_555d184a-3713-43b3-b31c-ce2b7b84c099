extends Node2D

class_name Worm

var grid_position: Vector3i = Vector3i.ZERO
var game_manager: GameManager
var current_direction: Vector2i = Vector2i.DOWN

@onready var sprite: Sprite2D = $Sprite2D

# Direction textures
var direction_textures = {
	Vector2i.UP: "res://assets/worm/upward.png",
	Vector2i.DOWN: "res://assets/worm/downward.png",
	Vector2i.LEFT: "res://assets/worm/right.png",  # We'll flip this horizontally
	Vector2i.RIGHT: "res://assets/worm/right.png"
}

# Movement input mapping
var input_to_direction = {
	"move_up": Vector2i.UP,
	"move_down": Vector2i.DOWN,
	"move_left": Vector2i.LEFT,
	"move_right": Vector2i.RIGHT
}

func _ready():
	# Create sprite if it doesn't exist
	if not sprite:
		sprite = Sprite2D.new()
		add_child(sprite)
	
	# Keep sprite at reasonable size but let transparent areas overlap
	sprite.scale = Vector2(0.06, 0.06)  # Slightly larger than grid to ensure good visibility

	# No offset needed - let the transparent areas naturally overlap
	sprite.offset = Vector2(0, 0)

	# Set initial direction and texture
	update_sprite_direction(Vector2i.DOWN)
	
	# Setup input map if not already done
	setup_input_map()

func setup_input_map():
	"""Setup input actions if they don't exist"""
	var input_map = InputMap
	
	# Define input actions
	var actions = {
		"move_up": [KEY_W, KEY_UP],
		"move_down": [KEY_S, KEY_DOWN],
		"move_left": [KEY_A, KEY_LEFT],
		"move_right": [KEY_D, KEY_RIGHT],
		"push_modifier": [KEY_SHIFT],
		"jump_modifier": [KEY_SPACE]
	}
	
	# Add actions to input map if they don't exist
	for action_name in actions:
		if not input_map.has_action(action_name):
			input_map.add_action(action_name)
			for key in actions[action_name]:
				var event = InputEventKey.new()
				event.keycode = key
				input_map.action_add_event(action_name, event)

func setup(manager: GameManager):
	"""Initialize the worm with game manager reference"""
	game_manager = manager
	grid_position = manager.worm_position

func _input(event):
	"""Handle input for movement"""
	if not game_manager:
		return

	# Check for movement input
	for action in input_to_direction:
		if Input.is_action_just_pressed(action):
			var direction = input_to_direction[action]
			var is_pushing = Input.is_action_pressed("push_modifier")
			var is_jumping = Input.is_action_pressed("jump_modifier")

			if is_jumping:
				attempt_jump(direction)
			else:
				attempt_move(direction, is_pushing)
			break

func attempt_move(direction: Vector2i, is_pushing: bool = false):
	"""Attempt to move the worm in the specified direction"""
	var target_pos = Vector3i(
		grid_position.x + direction.x,
		grid_position.y + direction.y,
		grid_position.z  # Worm stays on ground level
	)
	
	# Check if target position is valid
	if not game_manager.is_valid_position(target_pos):
		return
	
	# Update sprite direction
	update_sprite_direction(direction)
	
	# Check what's at the target position
	var entity_at_target = game_manager.get_entity_at_position(target_pos)
	
	if entity_at_target == null:
		# Empty space - move normally
		move_to_position(target_pos)
	elif entity_at_target is Bucket and is_pushing:
		# Bucket in the way and we're pushing
		var bucket = entity_at_target as Bucket
		if game_manager.push_bucket(bucket.grid_position, direction):
			# Bucket was pushed successfully, move to its old position
			move_to_position(target_pos)
	# If there's something in the way and we're not pushing, do nothing

func attempt_jump(direction: Vector2i):
	"""Attempt to jump in the specified direction"""
	var landing_pos = game_manager.can_jump_to_position(grid_position, direction)

	if landing_pos.x != -1:  # Valid landing position
		# Update sprite direction
		update_sprite_direction(direction)
		move_to_position(landing_pos)

func move_to_position(new_pos: Vector3i):
	"""Move the worm to a new position"""
	var old_pos = grid_position

	if game_manager.move_entity(self, old_pos, new_pos):
		grid_position = new_pos
		game_manager.worm_position = new_pos
		game_manager.worm_moved.emit(old_pos, new_pos)

func update_sprite_direction(direction: Vector2i):
	"""Update the sprite based on movement direction"""
	current_direction = direction
	
	if direction in direction_textures:
		var texture_path = direction_textures[direction]
		var texture = load(texture_path)
		if sprite and texture:
			sprite.texture = texture
			
			# Flip sprite horizontally for left direction
			if direction == Vector2i.LEFT:
				sprite.flip_h = true
			else:
				sprite.flip_h = false

func get_grid_position() -> Vector3i:
	"""Get the current grid position"""
	return grid_position

func get_current_direction() -> Vector2i:
	"""Get the current facing direction"""
	return current_direction

func _to_string() -> String:
	"""String representation for debugging"""
	return "Worm at %s facing %s" % [grid_position, current_direction]
