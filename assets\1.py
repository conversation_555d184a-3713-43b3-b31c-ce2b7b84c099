#!/usr/bin/env python3
"""
subject_transparent.py

Detect the main subject in an image, remove the background and save a transparent PNG.

**New in this version**
----------------------
1. **Batch processing**: pass a directory as the first argument to process every image inside (use `--recursive` for sub‑folders).
2. **--all** flag: one‑click enables `--denoise`, `--trim-white` and `--shrink 2`.

Examples
~~~~~~~~
```bash
# Single file with all clean‑ups
python subject_transparent.py hero.png --all

# Process every image in ./sprites (non‑recursive)
python subject_transparent.py sprites --all

# Process recursively and write outputs to ./out
python subject_transparent.py sprites out --all --recursive
```
"""

import argparse
import sys
from pathlib import Path
import cv2
import numpy as np

# Supported image extensions
IMG_EXTS = {".png", ".jpg", ".jpeg", ".webp", ".bmp", ".tiff"}


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(
        description="Extract subject & export transparent PNG (single file **or** whole directory)."
    )
    p.add_argument("input", help="Input image file *or* directory")
    p.add_argument(
        "output",
        nargs="?",
        help=(
            "Destination file (for single input) or directory (for directory input). "
            "If omitted, images are written next to originals with _transparent suffix."
        ),
    )

    # Processing parameters (same defaults as before)
    p.add_argument("--canny-th1", type=int, default=250)
    p.add_argument("--canny-th2", type=int, default=250)
    p.add_argument("--blur", type=int, default=5)
    p.add_argument("--feather", type=int, default=1)
    p.add_argument("--denoise", action="store_true")
    p.add_argument("--trim-white", action="store_true")
    p.add_argument("--shrink", type=int, default=2)
    p.add_argument("--white-threshold", type=int, default=220)

    # New helpers
    p.add_argument("--all", action="store_true", help="Enable --denoise --trim-white and --shrink 2")
    p.add_argument("--recursive", action="store_true", help="Traverse sub‑folders when input is a directory")
    return p


def remove_white_edges(image_bgra: np.ndarray, white_thresh: int) -> np.ndarray:
    """Set alpha=0 for RGB values >= white_thresh (near‑white halo removal)."""
    b, g, r, a = cv2.split(image_bgra)
    white_px = (b >= white_thresh) & (g >= white_thresh) & (r >= white_thresh)
    a[white_px] = 0
    return cv2.merge([b, g, r, a])


def extract_subject(image_bgr: np.ndarray, args) -> np.ndarray | None:
    """Return BGRA image with background removed; None if no contour found."""
    if args.denoise:
        image_bgr = cv2.medianBlur(image_bgr, 3)

    gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
    gray_blur = cv2.GaussianBlur(gray, (args.blur, args.blur), 0)
    edges = cv2.Canny(gray_blur, args.canny_th1, args.canny_th2)

    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    edges = cv2.erode(cv2.dilate(edges, kernel, 2), kernel, 2)
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        return None

    mask = np.zeros_like(gray, dtype=np.uint8)
    cv2.drawContours(mask, [max(contours, key=cv2.contourArea)], -1, 255, -1)

    if args.shrink > 0:
        sk = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (args.shrink * 2 + 1, args.shrink * 2 + 1))
        mask = cv2.erode(mask, sk, 1)

    if args.feather > 0:
        mask = cv2.GaussianBlur(mask, (args.feather * 2 + 1, args.feather * 2 + 1), 0)

    mask = cv2.normalize(mask, None, 0, 255, cv2.NORM_MINMAX)
    if args.trim_white:
        _, mask = cv2.threshold(mask, 20, 255, cv2.THRESH_BINARY)

    b, g, r = cv2.split(image_bgr)
    bgra = cv2.merge([b, g, r, mask])
    return remove_white_edges(bgra, args.white_threshold)


def process_file(src: Path, dest_dir: Path, args) -> None:
    img = cv2.imread(str(src), cv2.IMREAD_COLOR)
    if img is None:
        print(f"[skip] Cannot read {src}")
        return
    out = extract_subject(img, args)
    if out is None:
        print(f"[skip] No subject in {src}")
        return
    out_path = dest_dir / f"{src.stem}_transparent.png"
    cv2.imwrite(str(out_path), out, [cv2.IMWRITE_PNG_COMPRESSION, 3])
    print(f"[ok] {out_path}")


def main(argv=None):
    args = build_arg_parser().parse_args(argv)

    # Expand --all convenience flag
    if args.all:
        args.denoise = True
        args.trim_white = True
        if args.shrink == 0:
            args.shrink = 2

    in_path = Path(args.input)
    if not in_path.exists():
        print(f"[error] {in_path} not found", file=sys.stderr)
        sys.exit(1)

    if in_path.is_file():
        # Single‑file mode
        out_file = (
            Path(args.output)
            if args.output and Path(args.output).suffix
            else in_path.with_name(f"{in_path.stem}_transparent.png")
        )
        img = cv2.imread(str(in_path), cv2.IMREAD_COLOR)
        if img is None:
            print(f"[error] Cannot read {in_path}", file=sys.stderr)
            sys.exit(1)
        out = extract_subject(img, args)
        if out is None:
            print("[error] No subject detected", file=sys.stderr)
            sys.exit(1)
        cv2.imwrite(str(out_file), out, [cv2.IMWRITE_PNG_COMPRESSION, 3])
        print(f"[done] {out_file}")
        return

    # Directory mode
    dest_dir = Path(args.output) if args.output else in_path
    if dest_dir.suffix:
        print("[error] Output must be a directory when input is a folder", file=sys.stderr)
        sys.exit(1)
    dest_dir.mkdir(parents=True, exist_ok=True)

    iterator = in_path.rglob("*") if args.recursive else in_path.iterdir()
    for p in iterator:
        if p.is_file() and p.suffix.lower() in IMG_EXTS and not p.name.endswith("_transparent.png"):
            process_file(p, dest_dir, args)

    print("[done] Batch complete")


if __name__ == "__main__":
    main()
